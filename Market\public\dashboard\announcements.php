<?php
/**
 * Template for displaying Announcements in MarketKing Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Check if user is vendor
$user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()){
    $user_id = marketking()->get_team_member_parent();
}

if (!marketking()->is_vendor($user_id)) {
    wp_die(esc_html__('You do not have permission to access this page.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

$per_page = 10;
$paged = max(1, isset($_GET['current_page']) ? intval($_GET['current_page']) : 1);

$order_filter = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'DESC';
$search_filter = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$course_id = isset($_GET['course-id']) ? sanitize_text_field($_GET['course-id']) : '';
$date_filter = isset($_GET['date']) ? sanitize_text_field($_GET['date']) : '';

$year = !empty($date_filter) ? date('Y', strtotime($date_filter)) : '';
$month = !empty($date_filter) ? date('m', strtotime($date_filter)) : '';
$day = !empty($date_filter) ? date('d', strtotime($date_filter)) : '';

$args = array(
    'post_type' => 'tutor_announcements',
    'post_status' => 'publish',
    's' => $search_filter,
    'post_parent' => $course_id,
    'posts_per_page' => $per_page,
    'paged' => $paged,
    'orderby' => 'ID',
    'order' => $order_filter,
    'author' => $user_id,
);

if (!empty($date_filter)) {
    $args['date_query'] = array(
        array(
            'year' => $year,
            'month' => $month,
            'day' => $day,
        ),
    );
}

$the_query = new WP_Query($args);

// Get vendor's courses
$vendor_courses = get_posts(array(
    'post_type' => 'product',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
    'meta_query' => array(
        array(
            'key' => '_tutor_course_product_id',
            'compare' => 'EXISTS'
        )
    )
));

?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title"><?php esc_html_e('Duyurular', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                        </div>
                    </div>
                </div>

                <div class="card card-bordered">
                    <div class="card-inner">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="icon-circle icon-circle-lg bg-primary-dim">
                                    <em class="icon ni ni-bell"></em>
                                </div>
                            </div>
                            <div class="col">
                                <div class="text-muted mb-1">
                                    <?php esc_html_e('Duyuru Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                </div>
                                <div class="h6">
                                    <?php esc_html_e('Kurs öğrencilerinizi bilgilendirin', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#announcement-modal">
                                    <?php esc_html_e('Yeni Duyuru Ekle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3 mt-3">
                    <div class="col-lg-6">
                        <label class="form-label"><?php esc_html_e('Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <select class="form-select" name="course-filter" id="course-filter">
                            <option value=""><?php esc_html_e('Tümü', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <?php if ($vendor_courses) : ?>
                                <?php foreach ($vendor_courses as $course) : ?>
                                    <option value="<?php echo esc_attr($course->ID); ?>" <?php selected($course_id, $course->ID); ?>>
                                        <?php echo esc_html($course->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <option value=""><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label"><?php esc_html_e('Sıralama', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <select class="form-select" name="order-filter" id="order-filter">
                            <option value="DESC" <?php selected($order_filter, 'DESC'); ?>><?php esc_html_e('Azalan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <option value="ASC" <?php selected($order_filter, 'ASC'); ?>><?php esc_html_e('Artan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <input type="date" class="form-control" name="date-filter" id="date-filter" value="<?php echo esc_attr($date_filter); ?>">
                    </div>
                </div>

                <div class="card card-bordered mt-4">
                    <div class="card-inner">
                        <?php if ($the_query->have_posts()) : ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th><?php esc_html_e('Başlık', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('İşlemler', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                                            <?php 
                                            $announcement = get_post();
                                            $course = get_post($announcement->post_parent);
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo esc_html($announcement->post_title); ?></strong>
                                                    <div class="text-muted small"><?php echo wp_trim_words($announcement->post_content, 15); ?></div>
                                                </td>
                                                <td><?php echo $course ? esc_html($course->post_title) : esc_html__('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></td>
                                                <td><?php echo esc_html(get_the_date()); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-announcement" 
                                                                data-id="<?php echo esc_attr($announcement->ID); ?>"
                                                                data-title="<?php echo esc_attr($announcement->post_title); ?>"
                                                                data-content="<?php echo esc_attr($announcement->post_content); ?>"
                                                                data-course="<?php echo esc_attr($announcement->post_parent); ?>">
                                                            <?php esc_html_e('Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-announcement" 
                                                                data-id="<?php echo esc_attr($announcement->ID); ?>">
                                                            <?php esc_html_e('Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>

                            <?php
                            // Pagination
                            $total_pages = $the_query->max_num_pages;
                            if ($total_pages > 1) {
                                echo '<div class="mt-3">';
                                echo paginate_links(array(
                                    'base' => add_query_arg('current_page', '%#%'),
                                    'format' => '',
                                    'prev_text' => esc_html__('« Önceki', 'marketking-multivendor-marketplace-for-woocommerce'),
                                    'next_text' => esc_html__('Sonraki »', 'marketking-multivendor-marketplace-for-woocommerce'),
                                    'current' => $paged,
                                    'total' => $total_pages,
                                ));
                                echo '</div>';
                            }
                            ?>
                        <?php else : ?>
                            <div class="text-center py-5">
                                <em class="icon ni ni-bell-off" style="font-size: 3rem; color: #ccc;"></em>
                                <h5 class="mt-3"><?php esc_html_e('Henüz duyuru yok', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                                <p class="text-muted"><?php esc_html_e('İlk duyurunuzu oluşturmak için yukarıdaki butona tıklayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        <?php endif; ?>
                        <?php wp_reset_postdata(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Announcement Modal -->
<div class="modal fade" id="announcement-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php esc_html_e('Yeni Duyuru', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="announcement-form">
                <div class="modal-body">
                    <?php wp_nonce_field('marketking_announcement_nonce', 'announcement_nonce'); ?>
                    <input type="hidden" name="action" value="marketking_save_announcement">
                    <input type="hidden" name="announcement_id" id="announcement_id" value="">
                    
                    <div class="mb-3">
                        <label class="form-label"><?php esc_html_e('Kurs Seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <select class="form-select" name="announcement_course" id="announcement_course" required>
                            <?php if ($vendor_courses) : ?>
                                <?php foreach ($vendor_courses as $course) : ?>
                                    <option value="<?php echo esc_attr($course->ID); ?>">
                                        <?php echo esc_html($course->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <option value=""><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><?php esc_html_e('Duyuru Başlığı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <input type="text" class="form-control" name="announcement_title" id="announcement_title" 
                               placeholder="<?php esc_attr_e('Duyuru başlığı', 'marketking-multivendor-marketplace-for-woocommerce'); ?>" 
                               maxlength="255" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><?php esc_html_e('İçerik', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <textarea class="form-control" name="announcement_content" id="announcement_content" rows="6" 
                                  placeholder="<?php esc_attr_e('Duyuru içeriği...', 'marketking-multivendor-marketplace-for-woocommerce'); ?>" 
                                  required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <?php esc_html_e('Yayınla', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Filter functionality
    $('#course-filter, #order-filter, #date-filter').on('change', function() {
        var courseId = $('#course-filter').val();
        var order = $('#order-filter').val();
        var date = $('#date-filter').val();
        
        var url = new URL(window.location);
        url.searchParams.set('course-id', courseId);
        url.searchParams.set('order', order);
        url.searchParams.set('date', date);
        url.searchParams.delete('current_page');
        
        window.location.href = url.toString();
    });
    
    // Edit announcement
    $('.edit-announcement').on('click', function() {
        var id = $(this).data('id');
        var title = $(this).data('title');
        var content = $(this).data('content');
        var course = $(this).data('course');
        
        $('#announcement_id').val(id);
        $('#announcement_title').val(title);
        $('#announcement_content').val(content);
        $('#announcement_course').val(course);
        $('.modal-title').text('<?php esc_html_e('Duyuru Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
        
        $('#announcement-modal').modal('show');
    });
    
    // Reset modal on close
    $('#announcement-modal').on('hidden.bs.modal', function() {
        $('#announcement-form')[0].reset();
        $('#announcement_id').val('');
        $('.modal-title').text('<?php esc_html_e('Yeni Duyuru', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
    });
    
    // Save announcement
    $('#announcement-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || '<?php esc_html_e('Bir hata oluştu', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
                }
            },
            error: function() {
                alert('<?php esc_html_e('Bir hata oluştu', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
            }
        });
    });
    
    // Delete announcement
    $('.delete-announcement').on('click', function() {
        if (confirm('<?php esc_html_e('Bu duyuruyu silmek istediğinizden emin misiniz?', 'marketking-multivendor-marketplace-for-woocommerce'); ?>')) {
            var id = $(this).data('id');
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'marketking_delete_announcement',
                    announcement_id: id,
                    nonce: '<?php echo wp_create_nonce('marketking_announcement_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data || '<?php esc_html_e('Bir hata oluştu', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
                    }
                },
                error: function() {
                    alert('<?php esc_html_e('Bir hata oluştu', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
                }
            });
        }
    });
});
</script>
