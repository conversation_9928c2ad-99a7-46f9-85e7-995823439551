<?php
/**
 * Template for displaying Quiz Attempts in MarketKing Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Check if user is vendor
$user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()){
    $user_id = marketking()->get_team_member_parent();
}

if (!marketking()->is_vendor($user_id)) {
    wp_die(esc_html__('You do not have permission to access this page.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

// Check if viewing single attempt
if (isset($_GET['view_quiz_attempt_id'])) {
    $attempt_id = intval($_GET['view_quiz_attempt_id']);
    include 'quiz-attempt-details.php';
    return;
}

$per_page = 10;
$paged = max(1, isset($_GET['current_page']) ? intval($_GET['current_page']) : 1);
$offset = ($paged - 1) * $per_page;

$course_filter = isset($_GET['course-id']) ? sanitize_text_field($_GET['course-id']) : '';
$order_filter = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'DESC';
$date_filter = isset($_GET['date']) ? sanitize_text_field($_GET['date']) : '';

// Get vendor's courses
$vendor_courses = get_posts(array(
    'post_type' => 'product',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
    'meta_query' => array(
        array(
            'key' => '_tutor_course_product_id',
            'compare' => 'EXISTS'
        )
    )
));

$course_ids = array_map(function($course) {
    return get_post_meta($course->ID, '_tutor_course_product_id', true);
}, $vendor_courses);

// Get quiz attempts for vendor's courses
global $wpdb;

$where_clause = "WHERE 1=1";
$where_params = array();

if (!empty($course_ids)) {
    $course_ids_placeholder = implode(',', array_fill(0, count($course_ids), '%d'));
    $where_clause .= " AND course_id IN ($course_ids_placeholder)";
    $where_params = array_merge($where_params, $course_ids);
}

if (!empty($course_filter)) {
    $where_clause .= " AND course_id = %d";
    $where_params[] = intval($course_filter);
}

if (!empty($date_filter)) {
    $where_clause .= " AND DATE(attempt_started_at) = %s";
    $where_params[] = $date_filter;
}

$order_by = "ORDER BY attempt_id " . ($order_filter === 'ASC' ? 'ASC' : 'DESC');
$limit_clause = "LIMIT %d OFFSET %d";
$where_params[] = $per_page;
$where_params[] = $offset;

$query = "SELECT * FROM {$wpdb->prefix}tutor_quiz_attempts $where_clause $order_by $limit_clause";
$quiz_attempts = $wpdb->get_results($wpdb->prepare($query, $where_params));

// Get total count for pagination
$count_query = "SELECT COUNT(*) FROM {$wpdb->prefix}tutor_quiz_attempts $where_clause";
$count_params = array_slice($where_params, 0, -2); // Remove limit and offset
$total_attempts = $wpdb->get_var($wpdb->prepare($count_query, $count_params));

?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title"><?php esc_html_e('Sınav ve Denemeler', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                        </div>
                    </div>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-lg-6">
                        <label class="form-label"><?php esc_html_e('Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <select class="form-select" name="course-filter" id="course-filter">
                            <option value=""><?php esc_html_e('Tümü', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <?php if ($vendor_courses) : ?>
                                <?php foreach ($vendor_courses as $course) : ?>
                                    <?php $tutor_course_id = get_post_meta($course->ID, '_tutor_course_product_id', true); ?>
                                    <option value="<?php echo esc_attr($tutor_course_id); ?>" <?php selected($course_filter, $tutor_course_id); ?>>
                                        <?php echo esc_html($course->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <option value=""><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label"><?php esc_html_e('Sıralama', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <select class="form-select" name="order-filter" id="order-filter">
                            <option value="DESC" <?php selected($order_filter, 'DESC'); ?>><?php esc_html_e('Azalan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                            <option value="ASC" <?php selected($order_filter, 'ASC'); ?>><?php esc_html_e('Artan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <input type="date" class="form-control" name="date-filter" id="date-filter" value="<?php echo esc_attr($date_filter); ?>">
                    </div>
                </div>

                <div class="card card-bordered">
                    <div class="card-inner">
                        <?php if ($quiz_attempts) : ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th><?php esc_html_e('Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Sınav', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                            <th><?php esc_html_e('İşlemler', 'marketking-multivendor-marketplace-for-woocommerce'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($quiz_attempts as $attempt) : ?>
                                            <?php 
                                            $student = get_user_by('id', $attempt->user_id);
                                            $quiz = get_post($attempt->quiz_id);
                                            $course = get_post($attempt->course_id);
                                            
                                            $earned_percentage = 0;
                                            if ($attempt->earned_marks > 0 && $attempt->total_marks > 0) {
                                                $earned_percentage = number_format(($attempt->earned_marks * 100) / $attempt->total_marks, 2);
                                            }
                                            
                                            $attempt_info = maybe_unserialize($attempt->attempt_info);
                                            $passing_grade = isset($attempt_info['passing_grade']) ? intval($attempt_info['passing_grade']) : 0;
                                            
                                            $is_passed = $earned_percentage >= $passing_grade;
                                            $status_class = $is_passed ? 'text-success' : 'text-danger';
                                            $status_text = $is_passed ? esc_html__('Geçti', 'marketking-multivendor-marketplace-for-woocommerce') : esc_html__('Kaldı', 'marketking-multivendor-marketplace-for-woocommerce');
                                            ?>
                                            <tr>
                                                <td>
                                                    <?php if ($student) : ?>
                                                        <div class="user-card">
                                                            <div class="user-avatar bg-primary-dim">
                                                                <span><?php echo esc_html(strtoupper(substr($student->display_name, 0, 2))); ?></span>
                                                            </div>
                                                            <div class="user-info">
                                                                <span class="tb-lead"><?php echo esc_html($student->display_name); ?></span>
                                                                <span class="text-muted"><?php echo esc_html($student->user_email); ?></span>
                                                            </div>
                                                        </div>
                                                    <?php else : ?>
                                                        <span class="text-muted"><?php esc_html_e('Kullanıcı bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($quiz) : ?>
                                                        <strong><?php echo esc_html($quiz->post_title); ?></strong>
                                                    <?php else : ?>
                                                        <span class="text-muted"><?php esc_html_e('Sınav bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($course) : ?>
                                                        <?php echo esc_html($course->post_title); ?>
                                                    <?php else : ?>
                                                        <span class="text-muted"><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="progress-info">
                                                        <div class="progress-text">
                                                            <span class="<?php echo esc_attr($status_class); ?>">
                                                                <?php echo esc_html($earned_percentage); ?>%
                                                            </span>
                                                            <small class="text-muted">
                                                                (<?php echo esc_html($attempt->earned_marks); ?>/<?php echo esc_html($attempt->total_marks); ?>)
                                                            </small>
                                                        </div>
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar <?php echo $is_passed ? 'bg-success' : 'bg-danger'; ?>" 
                                                                 style="width: <?php echo esc_attr($earned_percentage); ?>%"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-dot <?php echo $is_passed ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $status_text; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-muted">
                                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($attempt->attempt_started_at))); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?php echo esc_url(add_query_arg('view_quiz_attempt_id', $attempt->attempt_id)); ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <?php esc_html_e('Detaylar', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <?php
                            // Pagination
                            $total_pages = ceil($total_attempts / $per_page);
                            if ($total_pages > 1) {
                                echo '<div class="mt-3">';
                                echo paginate_links(array(
                                    'base' => add_query_arg('current_page', '%#%'),
                                    'format' => '',
                                    'prev_text' => esc_html__('« Önceki', 'marketking-multivendor-marketplace-for-woocommerce'),
                                    'next_text' => esc_html__('Sonraki »', 'marketking-multivendor-marketplace-for-woocommerce'),
                                    'current' => $paged,
                                    'total' => $total_pages,
                                ));
                                echo '</div>';
                            }
                            ?>
                        <?php else : ?>
                            <div class="text-center py-5">
                                <em class="icon ni ni-file-text" style="font-size: 3rem; color: #ccc;"></em>
                                <h5 class="mt-3"><?php esc_html_e('Henüz sınav denemesi yok', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                                <p class="text-muted"><?php esc_html_e('Öğrencileriniz sınavlara girmeye başladığında burada görünecekler.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Filter functionality
    $('#course-filter, #order-filter, #date-filter').on('change', function() {
        var courseId = $('#course-filter').val();
        var order = $('#order-filter').val();
        var date = $('#date-filter').val();
        
        var url = new URL(window.location);
        url.searchParams.set('course-id', courseId);
        url.searchParams.set('order', order);
        url.searchParams.set('date', date);
        url.searchParams.delete('current_page');
        
        window.location.href = url.toString();
    });
});
</script>

<style>
.user-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.progress-info {
    min-width: 120px;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.progress-sm {
    height: 0.375rem;
}

.badge-dot {
    position: relative;
    padding-left: 1.25rem;
}

.badge-dot::before {
    content: '';
    position: absolute;
    left: 0.375rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 50%;
    background-color: currentColor;
}
</style>
