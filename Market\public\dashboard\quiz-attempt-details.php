<?php
/**
 * Template for displaying Quiz Attempt Details in MarketKing Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Check if user is vendor
$user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()){
    $user_id = marketking()->get_team_member_parent();
}

if (!marketking()->is_vendor($user_id)) {
    wp_die(esc_html__('You do not have permission to access this page.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

$attempt_id = isset($_GET['view_quiz_attempt_id']) ? intval($_GET['view_quiz_attempt_id']) : 0;

if (!$attempt_id) {
    wp_die(esc_html__('Geçersiz deneme ID\'si.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

global $wpdb;

// Get attempt data
$attempt = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}tutor_quiz_attempts WHERE attempt_id = %d",
    $attempt_id
));

if (!$attempt) {
    wp_die(esc_html__('Deneme bulunamadı.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

// Verify vendor owns this course
$vendor_courses = get_posts(array(
    'post_type' => 'product',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
    'meta_query' => array(
        array(
            'key' => '_tutor_course_product_id',
            'value' => $attempt->course_id,
            'compare' => '='
        )
    )
));

if (empty($vendor_courses)) {
    wp_die(esc_html__('Bu denemeye erişim yetkiniz yok.', 'marketking-multivendor-marketplace-for-woocommerce'));
}

$student = get_user_by('id', $attempt->user_id);
$quiz = get_post($attempt->quiz_id);
$course = get_post($attempt->course_id);

$earned_percentage = 0;
if ($attempt->earned_marks > 0 && $attempt->total_marks > 0) {
    $earned_percentage = number_format(($attempt->earned_marks * 100) / $attempt->total_marks, 2);
}

$attempt_info = maybe_unserialize($attempt->attempt_info);
$passing_grade = isset($attempt_info['passing_grade']) ? intval($attempt_info['passing_grade']) : 0;
$is_passed = $earned_percentage >= $passing_grade;

// Get quiz answers
$answers = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}tutor_quiz_question_answers WHERE quiz_attempt_id = %d ORDER BY answer_id",
    $attempt_id
));

?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <div class="nk-block-des text-soft">
                                <a href="<?php echo esc_url(remove_query_arg('view_quiz_attempt_id')); ?>" class="btn btn-outline-light btn-sm">
                                    <em class="icon ni ni-arrow-left"></em>
                                    <?php esc_html_e('Geri Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                </a>
                            </div>
                            <h3 class="nk-block-title page-title"><?php esc_html_e('Sınav Deneme Detayları', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                        </div>
                    </div>
                </div>

                <!-- Attempt Overview -->
                <div class="row g-gs">
                    <div class="col-lg-8">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <div class="card-head">
                                    <h5 class="card-title"><?php esc_html_e('Deneme Bilgileri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php if ($student) : ?>
                                                    <div class="user-card">
                                                        <div class="user-avatar bg-primary-dim">
                                                            <span><?php echo esc_html(strtoupper(substr($student->display_name, 0, 2))); ?></span>
                                                        </div>
                                                        <div class="user-info">
                                                            <span class="tb-lead"><?php echo esc_html($student->display_name); ?></span>
                                                            <span class="text-muted"><?php echo esc_html($student->user_email); ?></span>
                                                        </div>
                                                    </div>
                                                <?php else : ?>
                                                    <span class="text-muted"><?php esc_html_e('Kullanıcı bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Sınav', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php if ($quiz) : ?>
                                                    <strong><?php echo esc_html($quiz->post_title); ?></strong>
                                                <?php else : ?>
                                                    <span class="text-muted"><?php esc_html_e('Sınav bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php if ($course) : ?>
                                                    <?php echo esc_html($course->post_title); ?>
                                                <?php else : ?>
                                                    <span class="text-muted"><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Başlangıç Tarihi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($attempt->attempt_started_at))); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Bitiş Tarihi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php if ($attempt->attempt_ended_at) : ?>
                                                    <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($attempt->attempt_ended_at))); ?>
                                                <?php else : ?>
                                                    <span class="text-muted"><?php esc_html_e('Devam ediyor', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Süre', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-wrap">
                                                <?php 
                                                if ($attempt->attempt_ended_at) {
                                                    $start_time = strtotime($attempt->attempt_started_at);
                                                    $end_time = strtotime($attempt->attempt_ended_at);
                                                    $duration = $end_time - $start_time;
                                                    $hours = floor($duration / 3600);
                                                    $minutes = floor(($duration % 3600) / 60);
                                                    $seconds = $duration % 60;
                                                    
                                                    if ($hours > 0) {
                                                        echo sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
                                                    } else {
                                                        echo sprintf('%02d:%02d', $minutes, $seconds);
                                                    }
                                                } else {
                                                    echo esc_html__('Devam ediyor', 'marketking-multivendor-marketplace-for-woocommerce');
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <div class="card-head">
                                    <h5 class="card-title"><?php esc_html_e('Sonuçlar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                                </div>
                                
                                <div class="text-center">
                                    <div class="progress-circle progress-circle-lg" data-progress="<?php echo esc_attr($earned_percentage); ?>">
                                        <div class="progress-circle-fill <?php echo $is_passed ? 'bg-success' : 'bg-danger'; ?>"></div>
                                        <div class="progress-circle-text">
                                            <span class="h4 <?php echo $is_passed ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo esc_html($earned_percentage); ?>%
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <span class="badge badge-lg <?php echo $is_passed ? 'badge-success' : 'badge-danger'; ?>">
                                            <?php echo $is_passed ? esc_html__('Geçti', 'marketking-multivendor-marketplace-for-woocommerce') : esc_html__('Kaldı', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <span class="h5 text-primary"><?php echo esc_html($attempt->earned_marks); ?></span>
                                                    <div class="text-muted small"><?php esc_html_e('Alınan Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <span class="h5 text-dark"><?php echo esc_html($attempt->total_marks); ?></span>
                                                    <div class="text-muted small"><?php esc_html_e('Toplam Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <div class="text-muted small">
                                            <?php esc_html_e('Geçme Notu:', 'marketking-multivendor-marketplace-for-woocommerce'); ?> 
                                            <strong><?php echo esc_html($passing_grade); ?>%</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quiz Answers -->
                <?php if ($answers) : ?>
                <div class="card card-bordered mt-4">
                    <div class="card-inner">
                        <div class="card-head">
                            <h5 class="card-title"><?php esc_html_e('Soru ve Cevaplar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                        </div>
                        
                        <div class="accordion" id="quiz-answers-accordion">
                            <?php foreach ($answers as $index => $answer) : ?>
                                <?php 
                                $question = get_post($answer->question_id);
                                $question_type = get_post_meta($answer->question_id, 'question_type', true);
                                $is_correct = $answer->is_correct;
                                $answer_data = maybe_unserialize($answer->given_answer);
                                ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading-<?php echo esc_attr($index); ?>">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse-<?php echo esc_attr($index); ?>" aria-expanded="false">
                                            <div class="d-flex align-items-center w-100">
                                                <span class="me-2">
                                                    <?php if ($is_correct === '1') : ?>
                                                        <em class="icon ni ni-check-circle text-success"></em>
                                                    <?php elseif ($is_correct === '0') : ?>
                                                        <em class="icon ni ni-cross-circle text-danger"></em>
                                                    <?php else : ?>
                                                        <em class="icon ni ni-help-circle text-warning"></em>
                                                    <?php endif; ?>
                                                </span>
                                                <span class="flex-grow-1">
                                                    <?php esc_html_e('Soru', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <?php echo esc_html($index + 1); ?>: 
                                                    <?php echo $question ? esc_html(wp_trim_words($question->post_title, 10)) : esc_html__('Soru bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                </span>
                                                <span class="badge <?php echo $is_correct === '1' ? 'badge-success' : ($is_correct === '0' ? 'badge-danger' : 'badge-warning'); ?>">
                                                    <?php echo esc_html($answer->achieved_mark); ?>/<?php echo esc_html($answer->question_mark); ?>
                                                </span>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse-<?php echo esc_attr($index); ?>" class="accordion-collapse collapse" 
                                         data-bs-parent="#quiz-answers-accordion">
                                        <div class="accordion-body">
                                            <?php if ($question) : ?>
                                                <div class="mb-3">
                                                    <strong><?php esc_html_e('Soru:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                                                    <div class="mt-1"><?php echo wp_kses_post($question->post_content); ?></div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <strong><?php esc_html_e('Verilen Cevap:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                                                    <div class="mt-1">
                                                        <?php 
                                                        if (is_array($answer_data)) {
                                                            echo esc_html(implode(', ', $answer_data));
                                                        } else {
                                                            echo esc_html($answer_data);
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <strong><?php esc_html_e('Alınan Puan:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                                                        <span class="<?php echo $is_correct === '1' ? 'text-success' : 'text-danger'; ?>">
                                                            <?php echo esc_html($answer->achieved_mark); ?>/<?php echo esc_html($answer->question_mark); ?>
                                                        </span>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <strong><?php esc_html_e('Durum:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                                                        <?php if ($is_correct === '1') : ?>
                                                            <span class="text-success"><?php esc_html_e('Doğru', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                        <?php elseif ($is_correct === '0') : ?>
                                                            <span class="text-danger"><?php esc_html_e('Yanlış', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                        <?php else : ?>
                                                            <span class="text-warning"><?php esc_html_e('Değerlendirme Bekliyor', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.user-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.progress-circle-lg {
    width: 140px;
    height: 140px;
}

.progress-circle-fill {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(var(--bs-primary) calc(var(--progress) * 1%), #e9ecef 0);
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-circle-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 50%;
    width: 80%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize progress circles
    document.querySelectorAll('.progress-circle').forEach(function(circle) {
        const progress = circle.getAttribute('data-progress');
        circle.style.setProperty('--progress', progress);
    });
});
</script>
