<?php
/**
 * Simple test page for announcements
 */

if (!defined('ABSPATH')) { exit; }

?>
<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="card">
                    <div class="card-inner">
                        <h1>Duyurular Test Sayfası</h1>
                        <p>Bu sayfa çalışıyor!</p>
                        <p>User ID: <?php echo get_current_user_id(); ?></p>
                        <p>Tutor LMS: <?php echo (class_exists('TUTOR\Tutor') ? 'Yüklü' : '<PERSON>ük<PERSON><PERSON> değil'); ?></p>
<?php

$user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()){
    $user_id = marketking()->get_team_member_parent();
}

echo '<p>Vendor ID: ' . $user_id . '</p>';
echo '<p>Is Vendor: ' . (marketking()->is_vendor($user_id) ? 'Evet' : 'Hayır') . '</p>';

// Test courses
$courses = get_posts(array(
    'post_type' => 'courses',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
));

echo '<p>Tutor Courses: ' . count($courses) . '</p>';

$products = get_posts(array(
    'post_type' => 'product',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
));

echo '<p>Products: ' . count($products) . '</p>';

// Test announcements
$announcements = get_posts(array(
    'post_type' => 'tutor_announcements',
    'post_status' => 'any',
    'numberposts' => -1,
    'author' => $user_id,
));

echo '<p>Announcements: ' . count($announcements) . '</p>';

if (!empty($courses)) {
    echo '<h3>Kurslar:</h3>';
    foreach ($courses as $course) {
        echo '<p>- ' . $course->post_title . ' (ID: ' . $course->ID . ')</p>';
    }
}

if (!empty($announcements)) {
    echo '<h3>Duyurular:</h3>';
    foreach ($announcements as $announcement) {
        echo '<p>- ' . $announcement->post_title . ' (Course ID: ' . $announcement->post_parent . ')</p>';
    }
}
?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
